//
//  CurrencyRateView.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

/// 货币汇率管理视图
///
/// 提供货币汇率的查看、更新和管理功能。支持：
/// - 查看所有货币的当前汇率
/// - 手动更新汇率数据
/// - 设置本位币
/// - 批量操作货币项
/// - 从本地JSON和API获取汇率数据
struct CurrencyRateView: View {

  // MARK: - 环境属性
  @Environment(\.dismiss) private var dismiss

  /// 数据上下文 (仅用于写操作)
  @Environment(\.modelContext) private var modelContext

  /// 导航路径管理器
  @Environment(\.presentationMode) var presentationMode

  /// 路径管理器
  @EnvironmentObject private var pathManager: PathManagerHelper

  /// 集中式数据管理器
  @Environment(\.dataManager) private var dataManager

  /// ViewModel
  @ObservedObject private var viewModel: CurrencyRateVM

  // MARK: - Initialization

  init(viewModel: CurrencyRateVM) {
    self.viewModel = viewModel
  }

  var body: some View {

    VStack(spacing: 0) {
      // 标题栏
      NavigationBarKit(
        viewModel: NavigationBarKitVM(
          title: "货币汇率",
          backAction: {
            dataManager.hapticManager.trigger(.impactLight)
            dismiss()
          },
          rightButton: .icon(
            "arrow.clockwise",
            action: {
              if !viewModel.isLoadingRates {
                viewModel.fetchLatestExchangeRates(using: modelContext, dataManager: dataManager)
              }
            })
        )
      )

      // 搜索栏
      SearchBarKit(
        searchText: $viewModel.searchText,
        placeholder: "搜索货币名称或代码"
      )

      // 本位币选择按钮
      Button(action: {
        viewModel.showBaseCurrencySelection()
      }) {
        HStack(alignment: .center, spacing: 16) {
          // MARK: 左侧内容 (本位币图标)
          Image(systemName: "star.fill")
            .font(.system(size: 18, weight: .medium))
            .foregroundColor(.cAccentBlue)
            .frame(width: 40, height: 40)
            .background(.cAccentBlue.opacity(0.05))
            .cornerRadius(12)
            .overlay(
              RoundedRectangle(cornerRadius: 12)
                .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
            )

          // MARK: 中间内容 (本位币信息)
          VStack(alignment: .leading, spacing: 4) {
            Text("本位币")
              .font(.system(size: 14, weight: .medium))
              .foregroundColor(.cBlack)

            if let base = dataManager.currencies.first(where: { $0.isBaseCurrency }) {
              Text("\(base.code) · \(base.symbol)")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.6))
            } else {
              Text("请选择本位币")
                .font(.system(size: 12, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.6))
            }
          }

          Spacer()

          // MARK: 右侧内容 (本位币名称和箭头)
          HStack(spacing: 12) {
            if let base = dataManager.currencies.first(where: { $0.isBaseCurrency }) {
              Text(base.name)
                .font(.system(size: 14, weight: .regular))
                .foregroundColor(.cBlack.opacity(0.6))
            }

            Image(systemName: "chevron.forward")
              .font(.system(size: 12, weight: .medium))
              .foregroundColor(.cBlack.opacity(0.6))
          }
        }
        .padding(12)
        .background(.cWhite.opacity(0.5))
        .cornerRadius(24)
        .overlay(
          RoundedRectangle(cornerRadius: 16)
            .strokeBorder(.cAccentBlue.opacity(0.08), lineWidth: 1)
        )
      }
      .padding(.horizontal, 16)
      .padding(.top, 12)

      // 货币列表标题和操作按钮
      HStack {
        Text("货币列表")
          .font(.system(size: 16, weight: .medium))
          .foregroundColor(.cBlack)

        Spacer()

        HStack(spacing: 12) {
          // 全选/取消全选按钮
          ActionButton(
            title: viewModel.selectAllButtonTitle(from: dataManager),
            action: {
              viewModel.toggleSelectAll(using: modelContext, dataManager: dataManager)
            }
          )

          // 去重按钮（条件性显示）
          if viewModel.hasDuplicates(from: dataManager) {
            ActionButton(
              title: "去重",
              action: {
                viewModel.removeDuplicates(using: modelContext, dataManager: dataManager)
              }
            )
          }
        }
      }
      .padding(.horizontal, 16)
      .padding(.top, 24)
      .padding(.bottom, 8)

      // 货币列表
      ScrollView {
        LazyVStack(spacing: 12) {
          ForEach(viewModel.filteredCurrencies(from: dataManager)) { currency in
            CurrencyRateRow(
              viewModel: CurrencyRateRowVM(
                from: currency,
                onTap: {
                  viewModel.showCurrencyOperations(currency: currency)
                },
                onSelectionChanged: { newValue in
                  currency.isSelected = newValue
                  try? modelContext.save()
                  // 更新全选状态
                  viewModel.updateSelectAllState(dataManager: dataManager)
                }
              )
            )
            .contextMenu {
              Button {
                do {
                  try currency.setAsBaseCurrency(in: modelContext)
                } catch {
                  print("设置本位币失败: \(error)")
                }
              } label: {
                Label("设为本位币", systemImage: "star")
              }

              Button(role: .destructive) {
                // 如果要删除的是本位币，设置 UserDefaults 中的为默认值
                if currency.isBaseCurrency {
                  UserDefaults.standard.set("CNY", forKey: "baseCurrencyCode")
                }
                modelContext.delete(currency)
                try? modelContext.save()
              } label: {
                Label("删除", systemImage: "trash")
              }
            }
          }
        }
        .padding(.horizontal, 16)
        .padding(.top, 4)
      }
    }
    .background(.cLightBlue)
    .navigationBarTitleDisplayMode(.inline)
    .navigationBarBackButtonHidden(true)

    .onAppear {
      viewModel.checkBaseCurrency(using: modelContext, dataManager: dataManager)
    }
    .onChange(of: dataManager.currencies) {
      // 当货币数据发生变化时，更新全选状态
      viewModel.updateSelectAllState(dataManager: dataManager)
    }

    .floatingSheet(
      isPresented: $viewModel.showCurrencySheet,
      config: SheetBase(
        maxDetent: .fraction(0.5),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      if let currency = viewModel.selectedCurrency {
        CurrencyActionSheet(
          viewModel: CurrencyActionSheetVM(
            currency: currency,
            dataManager: dataManager,
            dismiss: { viewModel.showCurrencySheet = false },
            onSetAsBaseCurrency: { currency in
              viewModel.handleBaseCurrencySelection(currency)
            },
            onSaveCustomRate: { currency, customRateText in
              viewModel.saveCustomRate(for: currency, customRateText: customRateText) {
                // 保存成功后的处理
              }
            },
            onRestoreDefaultRate: { currency in
              viewModel.restoreDefaultRate(for: currency) {
                // 恢复成功后的处理
              }
            }
          )
        )
      }
    }
    .floatingSheet(
      isPresented: $viewModel.showBaseCurrencySheet,
      config: SheetBase(
        maxDetent: .fraction(0.7),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      SelectCurrencySheet(
        viewModel: SelectCurrencySheetVM(
          selectedCurrencyCode: .constant(
            dataManager.currencies.first(where: { $0.isBaseCurrency })?.code ?? "CNY"),
          currencySymbol: .constant(""),
          mode: "baseCurrencySelection",
          dataManager: dataManager,
          onBaseCurrencySelect: { newBaseCurrency in
            viewModel.handleBaseCurrencySelection(newBaseCurrency)
          },
          onDismiss: { viewModel.showBaseCurrencySheet = false }
        )
      )
    }
  }
}

#if DEBUG
  struct CurrencyRateView_Previews: PreviewProvider {
    static var previews: some View {
      Group {
        // 场景1: 单一本位币 - 基础场景
        CurrencyRateView(
          viewModel: CurrencyRateVM(
            dataManager: createBasicDataManager(),
            modelContext: ModelContext(try! ModelContainer(for: CurrencyModel.self)))
        )
        .withDataManager(createBasicDataManager())
        .previewDisplayName("基础场景 - 单一本位币")

        // 场景2: 多货币场景 - 包含常用货币
        CurrencyRateView(
          viewModel: CurrencyRateVM(
            dataManager: createMultiCurrencyDataManager(),
            modelContext: ModelContext(try! ModelContainer(for: CurrencyModel.self)))
        )
        .withDataManager(createMultiCurrencyDataManager())
        .previewDisplayName("多货币场景")

        // 场景3: 包含重复货币的场景
        CurrencyRateView(
          viewModel: CurrencyRateVM(
            dataManager: createDuplicateCurrencyDataManager(),
            modelContext: ModelContext(try! ModelContainer(for: CurrencyModel.self)))
        )
        .withDataManager(createDuplicateCurrencyDataManager())
        .previewDisplayName("包含重复货币")

        // 场景4: 空状态预览
        CurrencyRateView(
          viewModel: CurrencyRateVM(
            dataManager: createEmptyDataManager(),
            modelContext: ModelContext(try! ModelContainer(for: CurrencyModel.self)))
        )
        .withDataManager(createEmptyDataManager())
        .previewDisplayName("空状态")
      }
    }

    /// 创建基础预览数据（单一本位币）
    static func createBasicDataManager() -> DataManagement {
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        )
      ]

      return DataManagement(
        cards: createSampleCards(),
        mainCategories: createSampleMainCategories(),
        subCategories: createSampleSubCategories(),
        currencies: currencies,
        recentTransactions: createSampleTransactions(),
        allTransactions: createSampleTransactions(),
        chatMessages: []
      )
    }

    /// 创建多货币预览数据
    static func createMultiCurrencyDataManager() -> DataManagement {
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        ),
        CurrencyModel(
          name: "美元",
          code: "USD",
          symbol: "$",
          rate: 0.138,
          isBaseCurrency: false,
          order: 1
        ),
        CurrencyModel(
          name: "欧元",
          code: "EUR",
          symbol: "€",
          rate: 0.128,
          isBaseCurrency: false,
          order: 2
        ),
        CurrencyModel(
          name: "日元",
          code: "JPY",
          symbol: "¥",
          rate: 20.15,
          isBaseCurrency: false,
          order: 3
        ),
        CurrencyModel(
          name: "英镑",
          code: "GBP",
          symbol: "£",
          rate: 0.108,
          customRate: 0.110,  // 自定义汇率
          isBaseCurrency: false,
          isCustom: true,
          order: 4
        ),
        CurrencyModel(
          name: "港币",
          code: "HKD",
          symbol: "HK$",
          rate: 1.078,
          isBaseCurrency: false,
          isSelected: true,  // 选中状态
          order: 5
        ),
      ]

      return DataManagement(
        cards: createSampleCards(),
        mainCategories: createSampleMainCategories(),
        subCategories: createSampleSubCategories(),
        currencies: currencies,
        recentTransactions: createSampleTransactions(),
        allTransactions: createSampleTransactions(),
        chatMessages: []
      )
    }

    /// 创建包含重复货币的预览数据
    static func createDuplicateCurrencyDataManager() -> DataManagement {
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        ),
        CurrencyModel(
          name: "美元",
          code: "USD",
          symbol: "$",
          rate: 0.138,
          isBaseCurrency: false,
          order: 1
        ),
        CurrencyModel(
          name: "美元",  // 重复货币
          code: "USD",
          symbol: "$",
          rate: 0.140,
          isBaseCurrency: false,
          order: 2
        ),
        CurrencyModel(
          name: "欧元",
          code: "EUR",
          symbol: "€",
          rate: 0.128,
          isBaseCurrency: false,
          order: 3
        ),
        CurrencyModel(
          name: "欧元",  // 重复货币
          code: "EUR",
          symbol: "€",
          rate: 0.125,
          isBaseCurrency: false,
          order: 4
        ),
      ]

      return DataManagement(
        cards: createSampleCards(),
        mainCategories: createSampleMainCategories(),
        subCategories: createSampleSubCategories(),
        currencies: currencies,
        recentTransactions: createSampleTransactions(),
        allTransactions: createSampleTransactions(),
        chatMessages: []
      )
    }

    /// 创建空状态预览数据
    static func createEmptyDataManager() -> DataManagement {
      return DataManagement(
        cards: [],
        mainCategories: [],
        subCategories: [],
        currencies: [],
        recentTransactions: [],
        allTransactions: [],
        chatMessages: []
      )
    }

    // MARK: - 共享的示例数据创建方法

    /// 创建示例卡片数据
    static func createSampleCards() -> [CardModel] {
      return [
        CardModel(
          id: UUID(),
          order: 0,
          isCredit: false,
          isSelected: true,
          name: "招商银行储蓄卡",
          remark: "主要储蓄账户",
          currency: "CNY",
          symbol: "¥",
          balance: 12580.50,
          credit: 0,
          isStatistics: true,
          cover: "Card_CS_1",
          bankName: "招商银行",
          cardNumber: "1234",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(),
          order: 1,
          isCredit: true,
          isSelected: true,
          name: "工商银行信用卡",
          remark: "日常消费卡",
          currency: "CNY",
          symbol: "¥",
          balance: -2580.30,
          credit: 10000.0,
          isStatistics: true,
          cover: "Card_CS_2",
          bankName: "工商银行",
          cardNumber: "5678",
          billDay: 5,
          isFixedDueDay: true,
          dueDay: 25,
          createdAt: Date(),
          updatedAt: Date()
        ),
      ]
    }

    /// 创建示例主分类数据
    static func createSampleMainCategories() -> [TransactionMainCategoryModel] {
      return [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "expense_food",
          name: "餐饮",
          icon: .emoji("🍽️"),
          order: 1,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "工资收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]
    }

    /// 创建示例子分类数据
    static func createSampleSubCategories() -> [TransactionSubCategoryModel] {
      return [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "shopping_clothes",
          name: "服装",
          icon: .emoji("👕"),
          order: 1,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "food_restaurant",
          name: "餐厅",
          icon: .emoji("🏪"),
          order: 0,
          mainId: "expense_food"
        ),
        TransactionSubCategoryModel(
          id: "salary_main",
          name: "基本工资",
          icon: .emoji("💼"),
          order: 0,
          mainId: "income_salary"
        ),
      ]
    }

    /// 创建示例交易数据
    static func createSampleTransactions() -> [TransactionModel] {
      let cards = createSampleCards()
      let now = Date()
      let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: now) ?? now
      let twoDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: now) ?? now

      return [
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[0].id,
          transactionAmount: 128.50,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "超市购物",
          transactionDate: now,
          createdAt: now,
          updatedAt: now
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[1].id,
          transactionAmount: 8500.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "月度工资",
          transactionDate: yesterday,
          createdAt: yesterday,
          updatedAt: yesterday
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "food_restaurant",
          fromCardId: cards[1].id,
          transactionAmount: 89.90,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "晚餐",
          transactionDate: twoDaysAgo,
          createdAt: twoDaysAgo,
          updatedAt: twoDaysAgo
        ),
      ]
    }
  }
#endif
