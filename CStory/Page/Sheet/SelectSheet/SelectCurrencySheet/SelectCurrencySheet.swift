//
//  SelectCurrencySheet.swift
//  CStory
//
//  Created by NZUE on 2025/4/11.
//

import SwiftData
import SwiftUI

// MARK: - 货币选择 Sheet

/// 货币选择弹窗
///
/// 支持多种显示模式，用于不同场景的货币选择：
/// - all: 显示所有已选中的货币
/// - baseAndCard: 只显示本位币和卡片货币
/// - simpleConversion: 仅显示基础货币和备选货币
/// - baseCurrencySelection: 本位币选择模式
///
/// ## 功能特性
/// - 根据模式过滤货币列表
/// - 本位币优先显示
/// - 支持回调通知选择变化
struct SelectCurrencySheet: View {
  // MARK: - 属性

  /// ViewModel
  @ObservedObject private var viewModel: SelectCurrencySheetVM

  // MARK: - 初始化
  init(viewModel: SelectCurrencySheetVM) {
    self.viewModel = viewModel
  }

  var body: some View {

    VStack(spacing: 12) {
      // 标题栏
      SheetTitle(
        title: "选择货币", button: "xmark.circle.fill",
        rightButtonAction: {
          viewModel.handleDismiss()
        })

      // 货币列表
      ScrollView {
        LazyVStack(spacing: 12) {
          ForEach(viewModel.currencies) { currency in
            HStack(spacing: 12) {

              // 货币信息
              VStack(alignment: .leading, spacing: 2) {
                HStack(spacing: 8) {
                  Text(viewModel.getCurrencyDisplayName(currency))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(
                      viewModel.isCurrencySelected(currency)
                        ? .cAccentBlue : .cBlack
                    )

                  // 本位币选择模式下显示"当前"标识
                  if viewModel.shouldShowCurrentLabel(currency) {
                    Text("当前")
                      .font(.system(size: 12, weight: .medium))
                      .foregroundColor(.cWhite)
                      .padding(.horizontal, 8)
                      .padding(.vertical, 2)
                      .background(.cAccentBlue)
                      .cornerRadius(8)
                  }
                }

                Text("\(currency.code) · \(currency.symbol)")
                  .font(.system(size: 12))
                  .foregroundColor(.cBlack.opacity(0.6))
              }

              Spacer()

              // 选中状态标记
              if viewModel.isCurrencySelected(currency) {
                Image(systemName: "checkmark")
                  .font(.system(size: 14, weight: .semibold))
                  .foregroundColor(.cAccentBlue)
              }
            }
            .padding(.horizontal, 12)
            .frame(height: 52)
            .background(.cWhite.opacity(0.5))
            .cornerRadius(24)
            .overlay(
              RoundedRectangle(cornerRadius: 16)
                .strokeBorder(
                  viewModel.isCurrencySelected(currency)
                    ? .cAccentBlue : .cAccentBlue.opacity(0.08),
                  lineWidth: viewModel.isCurrencySelected(currency) ? 1.5 : 1
                )
            )
            .onTapGesture {
              viewModel.handleCurrencySelection(currency)
            }
          }
        }
        .padding(12)
      }
      Spacer()
    }

  }
}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct SelectCurrencySheet_Previews: PreviewProvider {
    static var previews: some View {
      SelectCurrencyPreviewContainer()
    }
  }

  struct SelectCurrencyPreviewContainer: View {
    @State private var showSheet = false
    @State private var selectedCurrencyCode = "CNY"
    @State private var currencySymbol = "¥"

    var body: some View {
      VStack {
        Button("选择货币") {
          showSheet = true
        }
        .padding()
        .background(.cAccentBlue)
        .foregroundColor(.white)
        .cornerRadius(12)

        Text("当前货币: \(selectedCurrencyCode) (\(currencySymbol))")
          .padding()
      }
      .frame(maxWidth: .infinity, maxHeight: .infinity)
      .background(.cLightBlue)
      .floatingSheet(
        isPresented: $showSheet,
        config: SheetBase(
          maxDetent: .fraction(0.7),
          cornerRadius: 24,
          interactiveDimiss: true,
          hPadding: 8,
          bPadding: 4
        )
      ) {
        SelectCurrencySheet(
          viewModel: SelectCurrencySheetVM(
            selectedCurrencyCode: $selectedCurrencyCode,
            currencySymbol: $currencySymbol,
            mode: "all",
            dataManager: Self.createPreviewDataManager(),
            onSelect: { code in
              selectedCurrencyCode = code
              showSheet = false
            },
            onDismiss: { showSheet = false }
          )
        )
      }
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        ),
        CurrencyModel(
          name: "美元",
          code: "USD",
          symbol: "$",
          rate: 7.2,
          isBaseCurrency: false,
          order: 1
        ),
        CurrencyModel(
          name: "欧元",
          code: "EUR",
          symbol: "€",
          rate: 7.8,
          isBaseCurrency: false,
          order: 2
        ),
      ]

      return DataManagement(
        cards: [],
        mainCategories: [],
        subCategories: [],
        currencies: currencies,
        recentTransactions: [],
        allTransactions: []
      )
    }
  }
#endif
